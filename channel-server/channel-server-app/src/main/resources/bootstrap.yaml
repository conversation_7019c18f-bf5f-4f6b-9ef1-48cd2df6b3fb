server:
  port: 9004
spring:
  application:
    name: channel-server
  profiles:
    active: @spring.profiles.active@
#    active: dev
  cloud:
    nacos:
      config:
        file-extension: yml
        group: channel
        shared-configs:
          - dataId: common.yml
            group: OA
            refresh: true


shenyu:
  client:
    http:
      props:
        contextPath: /channel-server


dubbo:
  consumer:
    scope: remote   # 强制走远程调用，禁用本地调用
